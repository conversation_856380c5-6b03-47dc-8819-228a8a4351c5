{"name": "fastscan-monorepo", "private": true, "version": "1.0.0", "packageManager": "yarn@4.9.1", "author": "max <<EMAIL>>", "license": "MIT", "workspaces": ["frontend", "backend", "nuclear_pond", "terraform", "lambda-nuclei-scanner"], "scripts": {"dev:frontend": "yarn workspace frontend dev", "build:frontend": "yarn workspace frontend build", "build:frontend:dev": "yarn workspace frontend build:dev", "build:frontend:staging": "yarn workspace frontend build:staging", "build:frontend:prod": "yarn workspace frontend build:prod", "deploy:frontend:dev": "yarn workspace frontend deploy:dev", "deploy:frontend:staging": "yarn workspace frontend deploy:staging", "deploy:frontend:prod": "yarn workspace frontend deploy:prod", "tf:destroy": "yarn workspace terraform destroy", "tf:plan": "yarn workspace terraform plan", "tf:apply": "yarn workspace terraform apply", "tf:frontend:plan": "cd terraform && terraform plan -target=module.frontend", "tf:frontend:apply": "cd terraform && terraform apply -target=module.frontend", "lambda:build": "cd lambda-nuclei-scanner && GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap", "lambda:test": "cd lambda-nuclei-scanner && go test ./...", "lambda:clean": "cd lambda-nuclei-scanner && rm -f bootstrap nuclei-lambda", "pack:terraform": "yarn dlx repomix -o repomix_terraform.md terraform/", "pack:docs": "yarn dlx repomix -o repomix_docs.md docs/"}}