# FastScan 

## Project Overview

- **Goal:** A security vulnerability scanning platform that leverages Nuclei scanner to identify security vulnerabilities, misconfigurations, and exposures in web applications and infrastructure.
- **Monorepo Tooling:** Standard package management with npm/yarn
- **Primary Technologies (Overall):**
  - Frontend: React, TypeScript, Vite, TailwindCSS, TanStack (React Query, Router, Form, Table)
  - Backend: Placeholder (not yet implemented)
  - Infrastructure: AWS Lambda, S3, DynamoDB, Glue, Terraform
  - Scanner: Nuclei (via Nuclear Pond)

## Monorepo Root Structure

- `/frontend/`: React-based web application for the user interface
- `/backend/`: Placeholder for future backend implementation
- `/nuclear_pond/`: Go-based implementation of Nuclear Pond, a cloud-based Nuclei scanner
- `/terraform/`: Terraform configuration for deploying Nuclear Pond infrastructure
- `/memory-bank/`: Memory bank for storing and retrieving information
- `/lambda-nuclei-scanner/`: Lambda function for the Nuclei scanner