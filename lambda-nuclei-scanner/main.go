package main

import (
	"bufio"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/google/uuid"
)

// Event is the input event for the Lambda function.
// Supports both uppercase and lowercase field names for compatibility
type Event struct {
	Targets []string `json:"targets"`
	Args    []string `json:"args"`
	Output  string   `json:"output"`

	// Support uppercase variants for compatibility
	TargetsUpper []string `json:"Targets"`
	ArgsUpper    []string `json:"Args"`
	OutputUpper  string   `json:"Output"`
}

// Response is the output response for the Lambda function.
type Response struct {
	Output string `json:"output"`
	Error  string `json:"error"`
}

// Variables for the nuclei binary, filesystem location, and temporary files
var (
	nucleiBinary = "/opt/nuclei"
	fileSystem   = "/tmp/"
	targetsFile  = fileSystem + "targets.txt"
	scanOutput   = fileSystem + "output.json"

	// Path to the config file in the Lambda layer (from configs_layer)
	nucleiConfigPath = "/opt/nuclei-config.yaml"
)

func handler(ctx context.Context, event Event) (Response, error) {
	// Handle uppercase field variants for compatibility
	if len(event.Targets) == 0 && len(event.TargetsUpper) > 0 {
		event.Targets = event.TargetsUpper
	}
	if len(event.Args) == 0 && len(event.ArgsUpper) > 0 {
		event.Args = event.ArgsUpper
	}
	if event.Output == "" && event.OutputUpper != "" {
		event.Output = event.OutputUpper
	}

	// Set the $HOME environment so nuclei can write inside of lambda
	homeDir := "/tmp" // /tmp is the only writable directory in Lambda
	err := os.Setenv("HOME", homeDir)
	if err != nil {
		log.Printf("Failed to set HOME env: %v", err)
		return Response{Error: fmt.Sprintf("Failed to set HOME env: %v", err)}, err
	}

	// Ensure the .config/nuclei directory and .nuclei-ignore file exist in HOME (/tmp)
	// to prevent Nuclei from erroring out.
	nucleiIgnoreFilePath := filepath.Join(homeDir, ".config", "nuclei", ".nuclei-ignore")
	nucleiConfigDirPath := filepath.Dir(nucleiIgnoreFilePath)

	// Create the directory
	if err := os.MkdirAll(nucleiConfigDirPath, 0755); err != nil {
		errMsg := fmt.Sprintf("Failed to create Nuclei config directory %s: %v", nucleiConfigDirPath, err)
		log.Println(errMsg)
		return Response{Error: errMsg}, err
	}
	log.Printf("Ensured Nuclei config directory exists: %s", nucleiConfigDirPath)

	// Create the .nuclei-ignore file if it doesn't exist
	if _, err := os.Stat(nucleiIgnoreFilePath); os.IsNotExist(err) {
		file, createErr := os.Create(nucleiIgnoreFilePath)
		if createErr != nil {
			errMsg := fmt.Sprintf("Failed to create .nuclei-ignore file %s: %v", nucleiIgnoreFilePath, createErr)
			log.Println(errMsg)
			return Response{Error: errMsg}, createErr
		}
		file.Close() // Close the newly created empty file
		log.Printf("Created empty .nuclei-ignore file: %s", nucleiIgnoreFilePath)
	} else if err != nil {
		// Some other error occurred with stat
		errMsg := fmt.Sprintf("Failed to stat .nuclei-ignore file %s: %v", nucleiIgnoreFilePath, err)
		log.Println(errMsg)
		return Response{Error: errMsg}, err
	} else {
		log.Printf(".nuclei-ignore file already exists: %s", nucleiIgnoreFilePath)
	}

	// Debug: Check Nuclei binary path and permissions
	if stat, err := os.Stat(nucleiBinary); err == nil {
		log.Printf("Nuclei binary found at %s, size: %d, mode: %v", nucleiBinary, stat.Size(), stat.Mode())
	} else {
		log.Printf("Nuclei binary NOT found at %s: %v", nucleiBinary, err)
	}

	// Debug: List /opt contents to verify layers are extracted properly
	log.Printf("Listing /opt contents...")
	if entries, err := os.ReadDir("/opt"); err == nil {
		for _, entry := range entries {
			log.Printf("/opt/%s (dir: %v)", entry.Name(), entry.IsDir())
		}
	} else {
		log.Printf("Failed to list /opt: %v", err)
	}

	// Debug: Check if we can find nuclei anywhere in the PATH
	if nucleiPath, err := exec.LookPath("nuclei"); err == nil {
		log.Printf("Nuclei found in PATH at: %s", nucleiPath)
	} else {
		log.Printf("Nuclei not found in PATH: %v", err)
	}

	// Base arguments for Nuclei: always use our config and custom templates path.
	baseNucleiArgs := []string{
		"-config", nucleiConfigPath,
		"-nt",                          // Add -nt flag to prevent Nuclei from trying to download/update templates
		"-duc",                         // Add -duc flag to disable automatic update checks for nuclei templates
		"-no-templates-update",         // Explicitly disable template updates
		"-no-templates-download",       // Explicitly disable template downloads
		"-disable-community-templates", // Disable community templates
		// "-t", customTemplatesPath, // Remove this; template paths are now defined in nuclei-config.yaml
	}

	var cmdArgs []string
	cmdArgs = append(cmdArgs, baseNucleiArgs...)

	// Append user-provided args from the event
	if len(event.Args) > 0 {
		cmdArgs = append(cmdArgs, event.Args...)
	}

	// Handle targets: -u for single, -l for multiple
	if len(event.Targets) == 0 {
		return Response{Error: "No targets provided"}, fmt.Errorf("no targets provided")
	}

	if len(event.Targets) == 1 {
		cmdArgs = append(cmdArgs, "-u", event.Targets[0])
	} else {
		err := writeTargetsToFile(event.Targets, targetsFile)
		if err != nil {
			log.Printf("Failed to write targets to file: %v", err)
			return Response{Error: fmt.Sprintf("Failed to write targets to file: %v", err)}, err
		}
		defer os.Remove(targetsFile) // Clean up the temp file
		cmdArgs = append(cmdArgs, "-l", targetsFile)
	}

	// Ensure Nuclei outputs JSON to a predictable file for further processing.
	// This allows S3 upload or direct JSON output based on event.Output.
	// For Nuclei v3.x, specifying -o with a .json extension is usually enough.
	// The separate -json flag caused issues with v3.1.7.
	cmdArgs = append(cmdArgs, "-o", scanOutput)

	// Debug: Check the Nuclei binary version and verify it's the correct one
	versionOutput, versionErr := runNuclei([]string{"--version"})
	log.Printf("Nuclei binary version check: %s", versionOutput)
	if versionErr != nil {
		log.Printf("Failed to get Nuclei version: %v", versionErr)
	}

	log.Printf("Running Nuclei with args: %v", cmdArgs)

	// Run the nuclei binary
	commandOutput, runErr := runNuclei(cmdArgs)
	if runErr != nil {
		// commandOutput here will contain stderr from Nuclei if runErr occurred
		log.Printf("Nuclei execution failed: %v. Output: %s", runErr, commandOutput)
		return Response{Output: commandOutput, Error: fmt.Sprintf("Nuclei execution failed: %v", runErr)}, runErr
	}

	// Process output based on event.Output type
	var finalOutput string
	var responseError string

	switch event.Output {
	case "s3":
		findings, err := jsonOutputFindings(scanOutput)
		if err != nil {
			log.Printf("Error reading or parsing findings from %s: %v", scanOutput, err)
			responseError = fmt.Sprintf("Error processing findings file: %v", err)
			finalOutput = "Error processing findings for S3 upload."
			break // Exit switch case
		}

		if len(findings) == 0 {
			log.Printf("No findings in %s to upload to S3.", scanOutput)
			finalOutput = "No findings to upload to S3."
			break // Exit switch case
		}

		bucketName := os.Getenv("BUCKET_NAME")
		if bucketName == "" {
			log.Printf("BUCKET_NAME environment variable not set. Cannot upload to S3.")
			responseError = "S3 bucket name not configured for Lambda."
			finalOutput = "S3 upload failed: configuration error."
			break
		}

		s3Path, uploadErr := writeAndUploadFindings(findings, bucketName)
		if uploadErr != nil {
			log.Printf("Error uploading findings to S3: %v", uploadErr)
			responseError = fmt.Sprintf("S3 upload error: %v", uploadErr)
			finalOutput = "Failed to upload findings to S3."
		} else if s3Path == "No findings to upload" { // Check for the specific message
			log.Printf("writeAndUploadFindings reported no findings to upload.")
			finalOutput = "No findings were uploaded to S3."
		} else {
			finalOutput = s3Path
			log.Printf("Successfully processed S3 output. Findings at: %s", s3Path)
		}
	case "json":
		fileContent, readErr := os.ReadFile(scanOutput)
		if readErr != nil {
			log.Printf("Error reading scan output file %s: %v", scanOutput, readErr)
			finalOutput = "Error reading scan output file."
			responseError = readErr.Error()
		} else {
			// Typically, for JSON output via Lambda, you'd base64 encode it if it's large or contains special chars.
			// finalOutput = base64.StdEncoding.EncodeToString(fileContent)
			finalOutput = string(fileContent) // For now, returning raw JSON string
			log.Printf("JSON output requested. Content from %s", scanOutput)
		}
	default: // "cmd" or unspecified
		finalOutput = base64.StdEncoding.EncodeToString([]byte(commandOutput)) // Encode to base64
		log.Printf("CMD output requested. Nuclei stdout/stderr (raw before base64): %s", commandOutput)
	}

	// Clean up the local JSON output file if it exists, after processing
	if _, statErr := os.Stat(scanOutput); statErr == nil {
		os.Remove(scanOutput)
	}

	return Response{Output: finalOutput, Error: responseError}, nil
}

// runNuclei executes the Nuclei binary with the given arguments.
// It returns the combined stdout and stderr, and an error if execution fails.
func runNuclei(args []string) (string, error) {
	cmd := exec.Command(nucleiBinary, args...)
	output, err := cmd.CombinedOutput() // Captures both stdout and stderr
	// No need to log here, the caller (handler) will log based on success/failure.
	return string(output), err
}

// writeTargetsToFile writes a list of targets to a specified file, one target per line.
func writeTargetsToFile(targets []string, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create targets file %s: %w", filename, err)
	}
	defer file.Close()

	w := bufio.NewWriter(file)
	for _, target := range targets {
		if _, err := fmt.Fprintln(w, target); err != nil {
			return fmt.Errorf("failed to write target '%s' to file %s: %w", target, filename, err)
		}
	}
	return w.Flush() // Ensure all buffered data is written to the file
}

// jsonFindings reads the output.json file and returns the findings
func jsonOutputFindings(scanOutputFile string) ([]interface{}, error) {
	file, err := os.Open(scanOutputFile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Create a scanner to read the file line by line
	scanner := bufio.NewScanner(file)

	// Iterate through the file and append the findings to the findings array
	var findings []interface{}
	for scanner.Scan() {
		var data interface{}
		if err := json.Unmarshal(scanner.Bytes(), &data); err != nil {
			// If there is an error, it might be a non-JSON line (e.g. Nuclei summary/error)
			// For now, we log it and skip. Depending on requirements, this could be handled differently.
			log.Printf("Skipping non-JSON line or unmarshal error in %s: %v", scanOutputFile, err)
			continue
		}
		findings = append(findings, data)
	}

	// Check for errors while reading the file
	if err := scanner.Err(); err != nil {
		return nil, err
	}

	// Return the findings
	return findings, nil
}

// Takes in []interface{}, iterates through it, writes it to a file based on the date, and uploads it to S3
func writeAndUploadFindings(findings []interface{}, bucketName string) (string, error) {
	// Bucket and region
	region := os.Getenv("AWS_REGION") // AWS_REGION should be available as an env var in Lambda
	// Iterate through the interface and convert to a slice of strings for writing to a file
	var s3Findings []string
	for _, finding := range findings {
		jsonFinding, err := json.Marshal(finding)
		if err != nil {
			return "", fmt.Errorf("failed to marshal finding for S3: %w", err)
		}
		s3Findings = append(s3Findings, string(jsonFinding))
	}

	if len(s3Findings) == 0 {
		return "No findings to upload", nil // Return a distinct message, not an error
	}

	// Two variables for filename, must be unique on execution, and s3 key partitioned with findings/year/month/day/hour/nuclei-findings-<timestamp>.json
	t := time.Now()
	uuidVal := uuid.New().String()
	s3Key := fmt.Sprintf("findings/%d/%02d/%02d/%02d/nuclei-findings-%s.json", t.Year(), t.Month(), t.Day(), t.Hour(), uuidVal)
	tempFilename := fmt.Sprintf("%snuclei-findings-s3-%s.json", fileSystem, uuidVal) // Ensure it's in /tmp/

	// Write the findings to a temporary file in /tmp/
	file, err := os.Create(tempFilename)
	if err != nil {
		return "", fmt.Errorf("failed to create temp file for S3 upload %s: %w", tempFilename, err)
	}
	// Ensure we attempt to close and remove the temp file
	defer os.Remove(tempFilename)
	defer file.Close()

	w := bufio.NewWriter(file)
	for _, finding := range s3Findings {
		if _, err := fmt.Fprintln(w, finding); err != nil {
			return "", fmt.Errorf("failed to write finding to temp file %s: %w", tempFilename, err)
		}
	}
	if err := w.Flush(); err != nil {
		return "", fmt.Errorf("failed to flush temp file %s: %w", tempFilename, err)
	}
	file.Close() // Close file before uploading

	// Upload the file to S3
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region)},
	)
	if err != nil {
		return "", fmt.Errorf("failed to create AWS session: %w", err)
	}

	// Create an uploader with the session and default options
	uploader := s3manager.NewUploader(sess)

	findingsFileToUpload, err := os.Open(tempFilename)
	if err != nil {
		return "", fmt.Errorf("failed to open temp file for S3 upload %s: %w", tempFilename, err)
	}
	defer findingsFileToUpload.Close()

	// Upload the file to S3.
	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(s3Key),
		Body:   findingsFileToUpload,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file %s to S3 bucket %s, key %s: %w", tempFilename, bucketName, s3Key, err)
	}

	// S3 path for the file
	s3uri := fmt.Sprintf("s3://%s/%s", bucketName, s3Key)

	log.Printf("Successfully uploaded findings to %s", s3uri)
	// Return the s3 uri after uploading
	return s3uri, nil
}

// Contains checks to see if a string is in a slice of strings
func contains(elems []string, v string) bool {
	for _, s := range elems {
		if v == s {
			return true
		}
	}
	return false
}

func main() {
	lambda.Start(handler)
}
