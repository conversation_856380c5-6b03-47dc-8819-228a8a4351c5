# Nuclei Lambda Scanner

A serverless Nuclei vulnerability scanner designed to run on AWS Lambda.

## Overview

This Lambda function provides a serverless interface to the Nuclei vulnerability scanner, allowing you to run security scans at scale without managing infrastructure. The function supports multiple output formats and can store results in S3 for further analysis.

## Features

- **Serverless Execution**: Runs on AWS Lambda with configurable memory and timeout
- **Multiple Output Formats**: Supports JSON, S3 upload, and command output
- **Template Management**: Uses custom Nuclei templates via Lambda layers
- **S3 Integration**: Automatic upload of findings to S3 with organized partitioning
- **Configuration Management**: Centralized Nuclei configuration via Lambda layers
- **Error Handling**: Comprehensive error handling and logging

## Architecture

The Lambda function consists of:

1. **Main Function**: Go-based Lambda handler (`main.go`)
2. **Lambda Layers**: 
   - Nuclei binary layer
   - Custom templates layer  
   - Configuration files layer
3. **S3 Storage**: Artifacts bucket for binaries, templates, and findings
4. **IAM Permissions**: Least-privilege access to required AWS services

## Input Event Format

```json
{
  "targets": ["https://example.com", "https://test.com"],
  "args": ["-severity", "high,critical"],
  "output": "s3"
}
```

### Parameters

- **targets** (required): Array of target URLs or IP addresses to scan
- **args** (optional): Additional Nuclei command-line arguments
- **output** (optional): Output format - "s3", "json", or "cmd" (default)

## Output Formats

### S3 Output (`"output": "s3"`)
Uploads findings to S3 with organized partitioning:
```
s3://bucket/findings/YYYY/MM/DD/HH/nuclei-findings-{uuid}.json
```

### JSON Output (`"output": "json"`)
Returns raw JSON findings directly in the Lambda response.

### Command Output (`"output": "cmd"`)
Returns base64-encoded command output (stdout/stderr).

## Environment Variables

- **BUCKET_NAME**: S3 bucket name for storing findings (required for S3 output)
- **AWS_REGION**: AWS region (automatically set by Lambda runtime)
- **HOME**: Set to `/tmp` for Nuclei configuration (automatically configured)

## Local Development

### Prerequisites

- Go 1.19 or later
- AWS CLI configured
- Docker (optional, for containerized testing)

### Building

```bash
# Build for Lambda runtime
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap

# Build for local testing
go build -o nuclei-lambda
```

### Testing Locally

```bash
# Install dependencies
go mod download

# Run tests
go test ./...

# Local execution (requires Nuclei binary in PATH)
./nuclei-lambda
```

### Docker Development

```bash
# Build container
docker build -t nuclei-lambda .

# Run container
docker run -e BUCKET_NAME=test-bucket nuclei-lambda
```

## Deployment

This Lambda function is primarily deployed via Terraform as part of the `nuclei_lambda` module, which handles the AWS infrastructure setup. For details on infrastructure deployment, refer to the `terraform/nuclei_lambda/README.md` documentation.

### Manual Deployment (Alternative)

If you need to deploy the Lambda package manually (e.g., for quick testing or specific scenarios outside of Terraform management):

1. Build the Go binary: `GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o bootstrap`
2. Create deployment package: `zip lambda.zip bootstrap`
3. Upload to AWS Lambda with runtime `provided.al2`

## Configuration

### Nuclei Configuration

The function uses a centralized Nuclei configuration file deployed via Lambda layers. The configuration is located at `/opt/nuclei-config.yaml` in the Lambda runtime.

### Custom Templates

Custom Nuclei templates are deployed via Lambda layers and available in the Lambda runtime. Template paths are configured in the Nuclei configuration file.

## Monitoring

The Lambda function provides comprehensive logging via CloudWatch:

- Execution logs with detailed error information
- Performance metrics (duration, memory usage)
- Custom metrics for scan results and errors

## Security

- **Least Privilege**: IAM role with minimal required permissions
- **Network Isolation**: Can be deployed in VPC for enhanced security
- **Encryption**: S3 storage encrypted at rest
- **Input Validation**: Comprehensive input validation and sanitization

## Troubleshooting

### Common Issues

1. **Build Failures**: Ensure Go 1.19+ and correct GOOS/GOARCH settings
2. **Permission Errors**: Verify IAM role has required S3 and CloudWatch permissions
3. **Timeout Issues**: Increase Lambda timeout for large scans
4. **Memory Issues**: Increase Lambda memory allocation

### Debugging

Enable detailed logging by setting log level in the Nuclei configuration:

```yaml
# nuclei-config.yaml
log-level: debug
```

## Dependencies

- `github.com/aws/aws-lambda-go`: AWS Lambda Go runtime
- `github.com/aws/aws-sdk-go`: AWS SDK for S3 operations
- `github.com/google/uuid`: UUID generation for unique filenames

## License

This project is licensed under the MIT License - see the main project LICENSE file for details.
