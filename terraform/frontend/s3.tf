# Local variables for computed values to avoid duplication and conditional issues
locals {
  cloudfront_domain = var.enable_cloudfront && length(aws_cloudfront_distribution.frontend) > 0 ? aws_cloudfront_distribution.frontend[0].domain_name : ""
  cloudfront_arn    = var.enable_cloudfront && length(aws_cloudfront_distribution.frontend) > 0 ? aws_cloudfront_distribution.frontend[0].arn : ""
  
  # Compute allowed origins for CORS based on deployment mode
  cors_origins = var.enable_cloudfront ? (
    # CloudFront mode: Allow CloudFront domain and custom domain if configured
    var.create_route53_record && var.frontend_domain_name != "" ? 
      concat(
        ["https://${var.frontend_domain_name}"],
        local.cloudfront_domain != "" ? ["https://${local.cloudfront_domain}"] : []
      ) : 
      local.cloudfront_domain != "" ? ["https://${local.cloudfront_domain}"] : []
  ) : [
    # Direct S3 mode: Allow S3 website endpoint and localhost for development
    "http://${aws_s3_bucket_website_configuration.frontend.website_endpoint}",
    "http://localhost:*",
    "http://127.0.0.1:*"
  ]
}

# S3 bucket for frontend static hosting
# This bucket hosts the React frontend files and serves them either:
# 1. Directly via S3 static website hosting (when CloudFront is disabled)
# 2. As origin for CloudFront distribution (when CloudFront is enabled)
resource "aws_s3_bucket" "frontend" {
  bucket = "${var.project_name}-frontend-${var.environment}"
  
  # Enable force destroy to allow Terraform to delete bucket even with contents
  # This is useful for development environments but should be carefully considered for production
  force_destroy = true

  tags = merge(var.tags, {
    Name        = "${var.project_name}-frontend-${var.environment}"
    Environment = var.environment
    Purpose     = "Frontend Static Hosting"
  })
}

# Enable versioning for the S3 bucket
# This helps with rollback scenarios and is a best practice
resource "aws_s3_bucket_versioning" "frontend" {
  bucket = aws_s3_bucket.frontend.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Configure the bucket for static website hosting
# This is needed for both CloudFront and direct S3 access scenarios
resource "aws_s3_bucket_website_configuration" "frontend" {
  bucket = aws_s3_bucket.frontend.id

  index_document {
    suffix = "index.html"
  }

  # For SPAs like React, redirect all 404s to index.html to handle client-side routing
  error_document {
    key = "index.html"
  }
}

# Block public access settings - we'll allow public access via bucket policy
# This is more secure than completely disabling block public access
resource "aws_s3_bucket_public_access_block" "frontend" {
  bucket = aws_s3_bucket.frontend.id

  block_public_acls       = true
  block_public_policy     = false  # We need this false to allow our bucket policy
  ignore_public_acls      = true
  restrict_public_buckets = false  # We need this false for public website hosting
}

# Set bucket policy based on CloudFront usage
# When CloudFront is enabled: Allow CloudFront OAC access + direct access for development/testing
# When CloudFront is disabled: Allow public read access for direct S3 website hosting
resource "aws_s3_bucket_policy" "frontend" {
  depends_on = [aws_s3_bucket_public_access_block.frontend]
  bucket     = aws_s3_bucket.frontend.id
  
  policy = var.enable_cloudfront ? data.aws_iam_policy_document.frontend_policy_with_cloudfront[0].json : data.aws_iam_policy_document.frontend_policy_direct.json
}

# Policy for when CloudFront is enabled
# Allows both CloudFront and direct access (useful for testing)
data "aws_iam_policy_document" "frontend_policy_with_cloudfront" {
  count = var.enable_cloudfront ? 1 : 0

  # Allow CloudFront to access objects
  statement {
    sid       = "AllowCloudFrontServicePrincipal"
    effect    = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "${aws_s3_bucket.frontend.arn}/*"
    ]
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceArn"
      values   = [aws_cloudfront_distribution.frontend[0].arn]
    }
  }
  
  # Also allow direct public read access for development/testing
  # Comment out this statement if you want CloudFront-only access
  statement {
    sid       = "PublicReadGetObject"
    effect    = "Allow"
    principals {
      type        = "*"
      identifiers = ["*"]
    }
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "${aws_s3_bucket.frontend.arn}/*"
    ]
  }
}

# Policy for direct S3 access (when CloudFront is disabled)
# Allows public read access for static website hosting
data "aws_iam_policy_document" "frontend_policy_direct" {
  statement {
    sid       = "PublicReadGetObject"
    effect    = "Allow"
    principals {
      type        = "*"
      identifiers = ["*"]
    }
    actions = [
      "s3:GetObject"
    ]
    resources = [
      "${aws_s3_bucket.frontend.arn}/*"
    ]
  }
}

# Configure CORS for the S3 bucket
# This allows the frontend to make API calls to your backend
resource "aws_s3_bucket_cors_configuration" "frontend" {
  bucket = aws_s3_bucket.frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    # Use computed origins from locals to handle conditional CloudFront references safely
    allowed_origins = local.cors_origins
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}
