# ==============================================================================
# CORE PROJECT VARIABLES
# ==============================================================================

variable "project_name" {
  description = "Name of the project, used for resource naming (e.g., 'fastscan', 'nuclei-scanner')"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project_name))
    error_message = "Project name must contain only lowercase letters, numbers, and hyphens."
  }
}

variable "environment" {
  description = "Deployment environment - affects resource naming and configuration"
  type        = string
  default     = "dev"
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "tags" {
  description = "Common tags to apply to all resources for cost tracking and organization"
  type        = map(string)
  default     = {}
  
  # Example:
  # tags = {
  #   Project     = "nuclei-scanner"
  #   Environment = "dev"
  #   Owner       = "security-team"
  #   CostCenter  = "security"
  # }
}

# ==============================================================================
# CLOUDFRONT AND CDN CONFIGURATION
# ==============================================================================

variable "enable_cloudfront" {
  description = <<-EOT
    Whether to create CloudFront distribution for the frontend.
    
    Set to:
    - true  = Production/staging deployments (global CDN, HTTPS, caching)
    - false = Development/testing deployments (direct S3 access, faster deployment)
  EOT
  type        = bool
  default     = false
}

variable "cloudfront_price_class" {
  description = <<-EOT
    CloudFront price class determines which edge locations are used:
    - PriceClass_All: All edge locations (best performance, highest cost)
    - PriceClass_200: US, Canada, Europe, Asia, Middle East, Africa
    - PriceClass_100: US, Canada, Europe (lowest cost, good for most cases)
  EOT
  type        = string
  default     = "PriceClass_100"
  
  validation {
    condition     = contains(["PriceClass_All", "PriceClass_200", "PriceClass_100"], var.cloudfront_price_class)
    error_message = "CloudFront price class must be one of: PriceClass_All, PriceClass_200, PriceClass_100."
  }
}

# ==============================================================================
# DOMAIN AND DNS CONFIGURATION
# ==============================================================================

variable "frontend_domain_name" {
  description = <<-EOT
    Custom domain name for the frontend (e.g., 'app.yourdomain.com', 'scanner.example.org').
    
    Leave empty to use default CloudFront or S3 URLs.
    
    Requirements when using custom domain:
    - You must own the domain
    - Domain must be managed in Route53 (or you handle DNS manually)
    - Certificate will be automatically created in ACM
    
    Examples:
    - app.yourdomain.com
    - scanner.example.org
    - nuclei.security.company.com
  EOT
  type        = string
  default     = ""
}

variable "create_route53_record" {
  description = <<-EOT
    Whether to automatically create Route53 DNS record for the custom domain.
    
    Set to true if:
    ✓ You specified a frontend_domain_name
    ✓ The domain is managed in Route53 in the same AWS account
    ✓ You want Terraform to handle DNS automatically
    
    Set to false if:
    ✗ You don't have a custom domain
    ✗ You manage DNS outside of Route53
    ✗ You want to create DNS records manually
  EOT
  type        = bool
  default     = false
}

variable "route53_zone_id" {
  description = <<-EOT
    Route53 hosted zone ID for the domain (required if create_route53_record = true).
    
    Find your zone ID:
    1. Go to Route53 in AWS Console
    2. Click on "Hosted zones"
    3. Find your domain and copy the "Hosted zone ID"
    
    Example: Z1234567890ABCDEF123
  EOT
  type        = string
  default     = ""
}

# ==============================================================================
# BACKEND API CONFIGURATION
# ==============================================================================

variable "api_url" {
  description = <<-EOT
    URL of the backend API that the frontend will connect to.
    
    This is typically the ALB DNS name from your Nuclear Pond deployment.
    The frontend will make API calls to this URL.
    
    Examples:
    - nuclei-alb-123456789.us-west-2.elb.amazonaws.com
    - api.yourdomain.com (if you have a custom domain for the API)
    - http://localhost:8080 (for local development)
  EOT
  type        = string
}

variable "api_key" {
  description = <<-EOT
    API key for authenticating with the Nuclear Pond service.
    
    This should match the API key configured in your Nuclear Pond deployment.
    The frontend will include this key in API requests.
    
    Security note: This value is sensitive and will be encrypted in Terraform state.
  EOT
  type        = string
  sensitive   = true
}

# ==============================================================================
# APPLICATION CONFIGURATION
# ==============================================================================

variable "demo_password" {
  description = <<-EOT
    Demo password for the frontend application login.
    
    This is used for demonstration purposes and basic access control.
    In production, consider implementing proper authentication.
    
    Security note: This value is sensitive and will be encrypted in Terraform state.
  EOT
  type        = string
  sensitive   = true
  default     = "TestPass"
}
