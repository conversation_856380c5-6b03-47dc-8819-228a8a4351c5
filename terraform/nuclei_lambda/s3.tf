# ==============================================================================
# S3 BUCKET CONFIGURATION FOR NUCLEI LAMBDA
# ==============================================================================

# ==============================================================================
# MAIN ARTIFACTS BUCKET
# ==============================================================================

# S3 bucket for storing Nuclei artifacts and scan findings
#tfsec:ignore:aws-s3-enable-bucket-logging tfsec:ignore:aws-s3-enable-versioning
resource "aws_s3_bucket" "artifacts_bucket" {
  bucket = "${var.project_name}-nuclei-artifacts"
  
  # Allow Terraform to delete the bucket and all objects during destroy
  force_destroy = true

  tags = merge(var.tags, {
    Name        = "${var.project_name}-nuclei-artifacts"
    Component   = "nuclei-lambda"
    Environment = var.environment
    Purpose     = "nuclei-artifacts-and-findings"
  })
}

# ==============================================================================
# BUCKET SECURITY CONFIGURATION
# ==============================================================================

# Server-side encryption configuration
#tfsec:ignore:aws-s3-encryption-customer-key
resource "aws_s3_bucket_server_side_encryption_configuration" "artifacts_encryption" {
  bucket = aws_s3_bucket.artifacts_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    
    bucket_key_enabled = true
  }
}

# Block all public access to the bucket
resource "aws_s3_bucket_public_access_block" "artifacts_public_access_block" {
  bucket = aws_s3_bucket.artifacts_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# ==============================================================================
# BUCKET VERSIONING (OPTIONAL)
# ==============================================================================

# Versioning configuration (optional)
resource "aws_s3_bucket_versioning" "artifacts_versioning" {
  count  = var.enable_s3_versioning ? 1 : 0
  bucket = aws_s3_bucket.artifacts_bucket.id
  
  versioning_configuration {
    status = "Enabled"
  }
}

# ==============================================================================
# LIFECYCLE CONFIGURATION
# ==============================================================================

# Lifecycle configuration for cost optimization
resource "aws_s3_bucket_lifecycle_configuration" "artifacts_lifecycle" {
  bucket = aws_s3_bucket.artifacts_bucket.id

  # Lifecycle rule for scan findings
  rule {
    id     = "findings_lifecycle"
    status = "Enabled"

    filter {
      prefix = "findings/"
    }

    # Transition to Infrequent Access after 30 days
    dynamic "transition" {
      for_each = var.findings_retention_days > 30 ? [1] : []
      content {
        days          = 30
        storage_class = "STANDARD_IA"
      }
    }

    # Transition to Glacier after 90 days
    dynamic "transition" {
      for_each = var.findings_retention_days > 90 ? [1] : []
      content {
        days          = 90
        storage_class = "GLACIER"
      }
    }

    # Delete findings after retention period (if specified)
    dynamic "expiration" {
      for_each = var.findings_retention_days > 0 ? [1] : []
      content {
        days = var.findings_retention_days
      }
    }

    # Clean up incomplete multipart uploads
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
  }

  # Lifecycle rule for old versions (if versioning is enabled)
  dynamic "rule" {
    for_each = var.enable_s3_versioning ? [1] : []
    content {
      id     = "old_versions_cleanup"
      status = "Enabled"

      noncurrent_version_transition {
        noncurrent_days = 30
        storage_class   = "STANDARD_IA"
      }

      noncurrent_version_transition {
        noncurrent_days = 90
        storage_class   = "GLACIER"
      }

      noncurrent_version_expiration {
        noncurrent_days = 365
      }
    }
  }

  # Lifecycle rule for artifacts (nuclei binary, templates, configs)
  dynamic "rule" {
    for_each = var.enable_s3_versioning ? [1] : []
    content {
      id     = "artifacts_lifecycle"
      status = "Enabled"

      filter {
        and {
          prefix = ""
          tags = {
            Type = "artifact"
          }
        }
      }

      # Clean up old versions if versioning is enabled
      noncurrent_version_expiration {
        noncurrent_days = 90
      }
    }
  }

  # Separate rule for cleaning up incomplete multipart uploads (cannot be combined with any filters)
  rule {
    id     = "cleanup_incomplete_uploads"
    status = "Enabled"

    # Clean up incomplete multipart uploads - no filter allowed for this action
    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }
  }
}

# ==============================================================================
# BUCKET NOTIFICATION (OPTIONAL)
# ==============================================================================

# Bucket notification configuration (for future use with EventBridge or SNS)
resource "aws_s3_bucket_notification" "artifacts_notification" {
  count  = var.enable_s3_notifications ? 1 : 0
  bucket = aws_s3_bucket.artifacts_bucket.id

  # Example: Notify when new findings are uploaded
  # This can be extended to trigger additional processing
  eventbridge = var.enable_s3_notifications
}

# ==============================================================================
# BUCKET POLICY (OPTIONAL)
# ==============================================================================

# Bucket policy for additional security (optional)
resource "aws_s3_bucket_policy" "artifacts_policy" {
  count  = var.enable_bucket_policy ? 1 : 0
  bucket = aws_s3_bucket.artifacts_bucket.id

  policy = data.aws_iam_policy_document.artifacts_bucket_policy[0].json
}

# Policy document for bucket access control
data "aws_iam_policy_document" "artifacts_bucket_policy" {
  count = var.enable_bucket_policy ? 1 : 0

  # Deny insecure transport
  statement {
    sid    = "DenyInsecureConnections"
    effect = "Deny"
    
    principals {
      type        = "*"
      identifiers = ["*"]
    }
    
    actions = ["s3:*"]
    
    resources = [
      aws_s3_bucket.artifacts_bucket.arn,
      "${aws_s3_bucket.artifacts_bucket.arn}/*"
    ]
    
    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values   = ["false"]
    }
  }

  # Allow access only from the Lambda execution role
  statement {
    sid    = "AllowLambdaAccess"
    effect = "Allow"
    
    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.lambda_role.arn]
    }
    
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:ListBucket"
    ]
    
    resources = [
      aws_s3_bucket.artifacts_bucket.arn,
      "${aws_s3_bucket.artifacts_bucket.arn}/*"
    ]
  }
}
