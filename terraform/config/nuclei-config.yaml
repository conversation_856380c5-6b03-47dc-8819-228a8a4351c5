# Headers to include with all HTTP request
header:
  - 'X-BugBounty-Hacker: github/nuclearpond'

# Directory based template execution
templates:
  - /opt # Tell <PERSON><PERSON><PERSON><PERSON> to look in the /opt directory for template files.
         # Custom templates layer unpacks .yaml files directly into /opt.

# Disable template updates and downloads
no-templates-update: true
no-templates-download: true

# Disable community templates to prevent auto-download attempts
disable-community-templates: true

# Rate Limit configuration
rate-limit: 500
bulk-size: 50
concurrency: 50

# Disable automatic updates
disable-update-check: true