# ==============================================================================
# NUCLEAR POND TERRAFORM CONFIGURATION EXAMPLE
# ==============================================================================
# Copy this file to terraform.tfvars and customize the values for your deployment
# cp terraform.tfvars.example terraform.tfvars

# ==============================================================================
# REQUIRED VARIABLES
# ==============================================================================

# Project name - must be unique as S3 bucket names are global
project_name = "your-project-name"

# API key for Nuclear Pond service - use a strong, unique key
nuclearpond_api_key = "your-secure-api-key-here"

# ==============================================================================
# NETWORK CONFIGURATION (Optional - uses defaults if not specified)
# ==============================================================================

# VPC and subnet CIDR blocks
# vpc_cidr = "10.0.0.0/16"
# public_subnet_az1_cidr = "10.0.1.0/24"
# public_subnet_az2_cidr = "10.0.2.0/24"
# private_subnet_az1_cidr = "10.0.3.0/24"
# private_subnet_az2_cidr = "10.0.4.0/24"

# Enable second NAT Gateway for high availability (increases costs)
# enable_nat_gateway_ha = false

# ==============================================================================
# NUCLEAR POND BACKEND CONFIGURATION
# ==============================================================================

# Enable the Nuclear Pond backend ECS service
enable_nuclear_pond_backend = true

# Environment name (dev, staging, prod)
nuclear_pond_environment = "dev"

# ECS task configuration
nuclear_pond_task_cpu = "256"        # CPU units (256, 512, 1024, 2048, 4096)
nuclear_pond_task_memory = "512"     # Memory in MB (512, 1024, 2048, 4096, 8192)
nuclear_pond_desired_count = 1       # Number of tasks to run

# Container configuration
nuclear_pond_container_port = 8080
nuclear_pond_health_check_path = "/health-check"

# Monitoring configuration
nuclear_pond_log_retention_days = 30

# Load balancer configuration
nuclear_pond_enable_deletion_protection = false

# ==============================================================================
# FRONTEND CONFIGURATION (Optional)
# ==============================================================================

# Enable frontend deployment
enable_frontend_deployment = true

# Frontend configuration
frontend_demo_password = "TestPass"

# Frontend environment (dev, staging, prod)
frontend_environment = "dev"

# Enable CloudFront for the frontend
enable_frontend_cloudfront = false

# ==============================================================================
# POW TARGETS CONFIGURATION (Optional)
# ==============================================================================

# Enable Proof of Work demo targets for testing
enable_pow_targets = false

# Domain name for PoW targets (required if enable_pow_targets = true)
# pow_domain_name = "your-domain.com"

# ==============================================================================
# LAMBDA CONFIGURATION
# ==============================================================================

# Nuclei version and architecture
nuclei_version = "3.1.7"
nuclei_arch = "linux_amd64"

# Lambda function configuration
nuclei_timeout = 900    # 15 minutes
memory_size = 512       # MB

# ==============================================================================
# COMMON TAGS
# ==============================================================================

tags = {
  Environment = "dev"
  Project     = "nuclear-pond"
  Owner       = "your-team"
  CostCenter  = "security"
}

# ==============================================================================
# ENVIRONMENT-SPECIFIC EXAMPLES
# ==============================================================================

# Development Environment Example:
# nuclear_pond_environment = "dev"
# nuclear_pond_task_cpu = "256"
# nuclear_pond_task_memory = "512"
# nuclear_pond_desired_count = 1
# nuclear_pond_log_retention_days = 7
# nuclear_pond_enable_deletion_protection = false

# Staging Environment Example:
# nuclear_pond_environment = "staging"
# nuclear_pond_task_cpu = "512"
# nuclear_pond_task_memory = "1024"
# nuclear_pond_desired_count = 2
# nuclear_pond_log_retention_days = 30
# nuclear_pond_enable_deletion_protection = false

# Production Environment Example:
# nuclear_pond_environment = "prod"
# nuclear_pond_task_cpu = "1024"
# nuclear_pond_task_memory = "2048"
# nuclear_pond_desired_count = 3
# nuclear_pond_log_retention_days = 90
# nuclear_pond_enable_deletion_protection = true
# enable_nat_gateway_ha = true
