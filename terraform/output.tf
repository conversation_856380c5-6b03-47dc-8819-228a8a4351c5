# ==============================================================================
# NUCLEI LAMBDA MODULE OUTPUTS
# ==============================================================================

output "nuclei_lambda_function_name" {
  description = "Name of the Nuclei Lambda function"
  value       = module.nuclei_lambda.lambda_function_name
}

output "nuclei_lambda_function_arn" {
  description = "ARN of the Nuclei Lambda function"
  value       = module.nuclei_lambda.lambda_function_arn
}

output "nuclei_s3_bucket_name" {
  description = "Name of the Nuclei artifacts S3 bucket"
  value       = module.nuclei_lambda.s3_bucket_name
}

output "nuclei_findings_s3_path" {
  description = "S3 path where Nuclei scan findings are stored"
  value       = module.nuclei_lambda.findings_s3_path
}

# Legacy outputs for backward compatibility
output "function_name" {
  description = "ARN of the Nuclei Lambda function (legacy - use nuclei_lambda_function_arn)"
  value       = module.nuclei_lambda.lambda_function_arn
}

output "dynamodb_state_table" {
  value = aws_dynamodb_table.scan_state_table.arn
}

# PoW outputs
output "pow_enabled" {
  description = "Whether the PoW infrastructure is enabled"
  value       = var.enable_pow_targets
}

output "pow_domain" {
  description = "The domain name used for PoW targets"
  value       = var.enable_pow_targets ? var.pow_domain_name : null
}

output "pow_zone_id" {
  description = "The Route53 hosted zone ID for the PoW domain"
  value       = var.enable_pow_targets ? module.pow_targets.pow_zone_id : null
}

output "pow_target_alb" {
  description = "ALB DNS name for PoW targets"
  value       = var.enable_pow_targets ? module.pow_targets.pow_alb_dns_name : null
}

output "pow_example_urls" {
  description = "Example URLs for Nuclei scanning (after DNS is configured)"
  value       = var.enable_pow_targets ? [
    "http://${var.pow_domain_name}",
    "http://test1.${var.pow_domain_name}",
    "http://test2.${var.pow_domain_name}",
    "http://random-subdomain.${var.pow_domain_name}"
  ] : null
}

# Network module outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.network.vpc_id
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = module.network.public_subnet_ids
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.network.private_subnet_ids
}

# Nuclear Pond Backend module outputs
output "nuclear_pond_backend_ecr_repository_url" {
  description = "URL of the ECR repository for Nuclear Pond"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].ecr_repository_url : null
}

output "nuclear_pond_backend_alb_dns_name" {
  description = "DNS name of the Application Load Balancer for Nuclear Pond service"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].alb_dns_name : null
}

output "nuclear_pond_backend_service_url" {
  description = "URL of the Nuclear Pond service"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].service_url : null
}

output "nuclear_pond_backend_health_check_url" {
  description = "Health check URL of the Nuclear Pond service"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].health_check_url : null
}

output "nuclear_pond_backend_ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].ecs_cluster_name : null
}

output "nuclear_pond_backend_ecs_service_name" {
  description = "Name of the ECS service"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].ecs_service_name : null
}

output "nuclear_pond_backend_deployment_info" {
  description = "Deployment information for the Nuclear Pond backend"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].deployment_info : null
}

# Legacy outputs for backward compatibility
output "nuclear_pond_ecr_repository_url" {
  description = "The URL of the ECR repository for Nuclear Pond (legacy - use nuclear_pond_backend_ecr_repository_url)"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].ecr_repository_url : null
}

output "nuclear_pond_alb_dns_name" {
  description = "The DNS name of the Application Load Balancer for Nuclear Pond service (legacy - use nuclear_pond_backend_alb_dns_name)"
  value       = var.enable_nuclear_pond_backend ? module.nuclear_pond_backend[0].alb_dns_name : null
}

# ==============================================================================
# FRONTEND MODULE OUTPUTS
# ==============================================================================

output "frontend_url" {
  description = "Primary URL to access the frontend application"
  value       = var.enable_frontend_deployment ? module.frontend[0].frontend_url : null
}

output "frontend_s3_bucket_name" {
  description = "Name of the S3 bucket hosting the frontend static files"
  value       = var.enable_frontend_deployment ? module.frontend[0].frontend_s3_bucket_name : null
}

output "frontend_s3_website_url" {
  description = "Full HTTP URL for S3 static website (used when CloudFront is disabled)"
  value       = var.enable_frontend_deployment ? module.frontend[0].frontend_s3_website_url : null
}

output "frontend_cloudfront_domain_name" {
  description = "CloudFront distribution domain name (e.g., d123456789.cloudfront.net)"
  value       = var.enable_frontend_deployment ? module.frontend[0].cloudfront_distribution_domain_name : null
}

output "frontend_cloudfront_distribution_id" {
  description = "CloudFront distribution ID (used for cache invalidation)"
  value       = var.enable_frontend_deployment ? module.frontend[0].cloudfront_distribution_id : null
}

output "frontend_all_urls" {
  description = "All available URLs to access the frontend (for testing and reference)"
  value       = var.enable_frontend_deployment ? module.frontend[0].all_frontend_urls : null
}

output "frontend_deployment_info" {
  description = "Summary of frontend deployment configuration and next steps"
  value       = var.enable_frontend_deployment ? module.frontend[0].deployment_info : null
}