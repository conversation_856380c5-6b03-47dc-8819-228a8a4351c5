{"name": "nuclear_pond", "private": true, "version": "0.1.0", "description": "Nuclear Pond", "scripts": {"build": "go build -o nuclearpond .", "run:local": "./nuclearpond local -l targets.txt -a LXQgLg== -c 10 -b 10 -p ./templates/", "download-templates": "node scripts/download-templates.js", "install:nuclei": "node scripts/install-nuclei.js", "run:nuclei": "bin/nuclei -l targets.txt -t ./templates/", "test": "go test ./pkg/server -v"}, "dependencies": {"axios": "^1.6.2", "csv-parse": "^5.5.2"}}