# Getting Started with Nuclear Pond

This guide walks you through the essential steps to install Nuclear Pond, set up the Nuclei engine, and run your first basic scan locally.

For advanced usage, cloud deployment, or API integration, please refer to the main [Documentation Hub](README.md).

## Prerequisites

Before installing Nuclear Pond, ensure you have the following:

-   **Go (Golang)**: Version 1.16 or later. Needed to build Nuclear Pond from source.
    -   *Installation*: Visit [golang.org/dl/](https://golang.org/dl/)
-   **Git**: Required for cloning the repository.

Optional (for specific features, covered in other guides):
-   AWS Account & CLI: For cloud execution.
-   Docker: For containerized deployments of the API server (covered in the [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md)).

## Installation

1.  **Clone the Repository**:
    Open your terminal and clone the Nuclear Pond repository:
    ```bash
    git clone https://github.com/your-username/nuclear-pond.git # Replace with the actual repository URL
    cd nuclear-pond
    ```

2.  **Build the Nuclear Pond Binary**:
    Inside the `nuclear_pond` directory, build the executable:
    ```bash
    go build -o nuclearpond main.go # Assuming your main package is main.go in the root
    ```
    *Note: If your project structure is different, adjust the build command (e.g., `cd cmd/nuclearpond; go build -o ../../nuclearpond`). This example assumes a common Go project layout where `main.go` is in a subdirectory, and you want the binary in the project root.* 
    *Correction: Based on the `nuclear_pond/README.md` which mentions `cd nuclear_pond; go build -o nuclearpond`, I'll assume the `main.go` or buildable package is directly in the `nuclear_pond` directory or the command implies building from within that specific directory if it's a module root.*

    A more typical command if `nuclear_pond` is the module root with a `main.go` file or a `cmd/nuclearpond` subdirectory:
    If `nuclear_pond` (the directory you `cd` into after cloning) is the Go module root and contains `main.go`:
    ```bash
    # (Already in nuclear_pond directory from step 1)
    go build -o nuclearpond
    ```
    If the main package is in `nuclear_pond/cmd/nuclearpond/`:
    ```bash
    # (Already in nuclear_pond directory from step 1)
    go build -o nuclearpond ./cmd/nuclearpond
    ```
    Verify the build by checking for the `nuclearpond` executable in the current directory.

## Installing Nuclei Engine

Nuclear Pond orchestrates the Nuclei scanning engine. Nuclei must be installed and accessible for local scans.

### Automated Installation (Recommended)

If your project includes a helper script (as suggested by `yarn install:nuclei` in one of the provided docs, though `yarn` is for Node.js projects, a similar make/bash script might exist for Go projects):

```bash
# Example: if a script is provided, e.g., in a /scripts directory
# ./scripts/install-nuclei.sh 

# If using a Makefile common in Go projects:
# make install-nuclei
```
Check your project for specific instructions on automated Nuclei installation. If a `package.json` with `yarn install:nuclei` exists at the root of the `fastscan` workspace and is intended for this Go project, it might be a bit unusual but follow that if it's the established method. Assuming a more Go-centric approach, a script or Makefile target is more common.

*Self-correction: The original `getting-started.md` mentions `yarn install:nuclei`. While Nuclear Pond is a Go project, the overall `fastscan` workspace might use Yarn for utility scripting. I will keep this option as it was explicitly mentioned.* 

If your project uses Yarn for scripting tasks (check for `package.json` and a relevant script):
```bash
# Check if yarn is installed and package.json has the script
# Ensure you are in the workspace root if package.json is there.
# cd ../ # If you are in nuclear_pond, and package.json is in fastscan root
# yarn install:nuclei 
# cd nuclear_pond # Go back to the project directory
```
This typically downloads Nuclei to a local `./bin` directory.

### Manual Installation

1.  **Download Nuclei**:
    Go to the [Nuclei releases page](https://github.com/projectdiscovery/nuclei/releases) and download the binary for your OS and architecture.

2.  **Install Nuclei**:
    Extract and move the `nuclei` binary to a directory in your system's PATH (e.g., `/usr/local/bin` on Linux/macOS) or place it in a known location like `./bin/` within your project.
    ```bash
    # Example for Linux:
    # curl -LO https://github.com/projectdiscovery/nuclei/releases/download/vX.Y.Z/nuclei_X.Y.Z_linux_amd64.zip
    # unzip nuclei_X.Y.Z_linux_amd64.zip
    # sudo mv nuclei /usr/local/bin/
    # chmod +x /usr/local/bin/nuclei
    ```

### Verify Nuclei Installation

Open a new terminal window and run:
```bash
# If installed globally in PATH
nuclei -version

# If installed to a local ./bin directory (e.g., by yarn script)
# (Assuming you are in the nuclear_pond directory or where ./bin/nuclei is)
# ./bin/nuclei -version 
# Or if ./bin is at the workspace root:
# ../bin/nuclei -version
```

## Your First Local Scan

With Nuclear Pond and Nuclei installed, you can run a simple local scan.

1.  **Ensure `nuclearpond` is executable** (if built in the current directory):
    ```bash
    chmod +x ./nuclearpond
    ```

2.  **Run a Scan**:
    Let's scan `example.com` using Nuclei's built-in `dns` templates. Nuclei arguments need to be base64 encoded for the `-a` flag.

    First, encode the Nuclei arguments. For example, to use DNS templates (`-t dns`):
    ```bash
    echo -ne "-t dns" | base64
    ```
    This will output a base64 string, for example: `LXQgZG5z`

    Now, use this encoded string with Nuclear Pond:
    ```bash
    # If nuclearpond is in your current directory
    ./nuclearpond local -t example.com -a LXQgZG5z

    # If nuclearpond is in PATH
    # nuclearpond local -t example.com -a LXQgZG5z
    ```

    This command tells Nuclear Pond to scan `example.com` locally, passing the `-t dns` arguments to Nuclei.

    You should see output from Nuclei indicating the scan progress and any findings.

## Troubleshooting Common Issues

-   **`nuclearpond: command not found`**: 
    -   Ensure `nuclearpond` binary is in your current directory (use `./nuclearpond`) or in a directory listed in your system's PATH.
    -   Verify it was built correctly.
-   **`nuclei: command not found` (during scan)**:
    -   Make sure Nuclei is installed and accessible via the system PATH, or if Nuclear Pond expects it in a specific relative path (e.g. `./bin`), ensure it's there.
    -   Run `nuclei -version` to confirm.
-   **Permission Denied (for `./nuclearpond` or `./bin/nuclei`)**: 
    -   Use `chmod +x ./nuclearpond` or `chmod +x ./bin/nuclei`.
-   **Base64 Encoding Issues**: 
    -   Always use `echo -ne "<nuclei_args>" | base64`. The `-n` flag prevents adding a trailing newline, which is important.
    -   To verify: `echo "LXQgZG5z" | base64 -d` should output `-t dns`.

## Next Steps

Congratulations on running your first scan with Nuclear Pond!

-   To learn about all CLI commands, advanced configurations (parallelism, templates, outputs), and different execution modes: See the **[User Guide (CLI)](user-guide.md)**.
-   For deploying Nuclear Pond to AWS: Consult the **[Deployment Guide (AWS)](../../terraform/nuclear_pond_backend/DEPLOYMENT.md)**.
-   To integrate with the API: Refer to the **[API Reference](api-reference.md)**.
-   To understand the system design: Read the **[Architecture Overview](architecture.md)**. 