# Nuclear Pond Architecture

This document provides an overview of the Nuclear Pond system architecture, its components, and how different execution modes function.

## Core Concepts

Nuclear Pond is designed to orchestrate [Nuclei](https://github.com/projectdiscovery/nuclei) scans across various environments. The primary goals of its architecture are scalability, flexibility, and cost-effectiveness.

It achieves this through distinct operational modes:

1.  **Local Execution Mode**: For direct, parallelized scanning on a single machine.
2.  **Cloud Execution Mode**: For distributed, highly scalable scanning using AWS Lambda.
3.  **API Service Mode**: Provides an HTTP interface to manage and initiate scans in either local or cloud mode, offering a persistent control plane and state management.

## Component Overview

### 1. Nuclear Pond CLI

-   **Purpose**: The primary user interface for interacting with Nuclear Pond.
-   **Commands**:
    -   `nuclearpond local`: Manages local scans.
    -   `nuclearpond run`: Manages cloud-based scans (currently AWS Lambda).
    -   `nuclearpond service`: Starts the HTTP API server.
-   **Responsibilities**: Parses user input, prepares targets and Nuclei arguments, invokes Nuclei (directly or via cloud services/API), and handles output.

### 2. Nuclei Engine

-   **Purpose**: The underlying scanning engine developed by Project Discovery.
-   **Integration**: Nuclear Pond wraps and orchestrates Nuclei. It does not modify Nuclei itself but rather controls its execution and parameters.

### 3. Local Execution Orchestrator

-   **Activation**: Used by the `nuclearpond local` command.
-   **Functionality**: 
    -   Manages a pool of worker threads.
    -   Distributes targets (or batches of targets) to local Nuclei processes.
    -   Collects and aggregates results.
-   **Dependencies**: Requires Nuclei to be installed and accessible in the system's PATH or a specified location.
-   **Use Case**: Development, small to medium-scale scans, environments without cloud access.

### 4. Cloud Execution Orchestrator (AWS Lambda)

-   **Activation**: Used by the `nuclearpond run` command and can be invoked via the API server when in `cloud` mode.
-   **Components**:
    -   **AWS Lambda Function**: The core execution unit. Each Lambda invocation runs Nuclei against a batch of targets. The Lambda package includes Nuclei and necessary runtime components.
    -   **S3 Bucket**: Used for storing scan results when the `s3` output option is selected. Can also be used to stage large target lists or custom Nuclei templates if needed (though template management primarily relies on Nuclei's native capabilities).
    -   **IAM Roles**: Define permissions for the Lambda function (e.g., to write to S3, CloudWatch Logs) and for the user/tool invoking the Lambda function.
-   **Functionality**:
    -   The CLI (or API server) prepares batches of targets and Nuclei arguments.
    -   It invokes multiple Lambda functions in parallel.
    -   Each Lambda function executes Nuclei with its assigned batch.
    -   Results are returned to the invoker or written to S3.
-   **Use Case**: Large-scale, highly parallelized scanning, serverless operations.

### 5. API Server

-   **Activation**: Started by the `nuclearpond service` command.
-   **Functionality**:
    -   Provides HTTP endpoints for scan management (start scan, get status).
    -   Authenticates requests using a bearer token (`NUCLEARPOND_API_KEY`).
    -   Can initiate scans in either `local` or `cloud` mode based on the API request.
    -   **State Management (DynamoDB)**: When the API server is used, it leverages an AWS DynamoDB table (`AWS_DYNAMODB_TABLE`) to store and track the state of scans (ID, status, targets, results summary, timestamps). This allows for persistent scan tracking even if the API client disconnects.
-   **Dependencies**:
    -   For `local` mode scans via API: Requires Nuclei accessible to the server environment.
    -   For `cloud` mode scans via API: Requires AWS credentials and configuration for Lambda invocation, S3 access, and DynamoDB access.
-   **Use Case**: Programmatic integration, automated workflows, CI/CD pipelines, providing a persistent interface to the scanning capabilities.

## Data Flow Examples

### Local Scan via CLI

1.  User runs `nuclearpond local -t example.com -a <nuclei_args_base64>`.
2.  CLI parses arguments.
3.  Local orchestrator spawns Nuclei processes based on `-c` (threads) and `-b` (batch_size).
4.  Nuclei scans `example.com`.
5.  Results are streamed to standard output.

### Cloud Scan via CLI (to S3)

1.  User runs `nuclearpond run -l targets.txt -a <nuclei_args_base64> -o s3`.
2.  CLI reads `targets.txt`, batches targets.
3.  For each batch, CLI invokes the configured AWS Lambda function with targets and Nuclei arguments.
4.  Lambda function executes Nuclei.
5.  Nuclei writes results to a unique S3 path (configured within the Lambda environment or passed).
6.  S3 paths for results are returned to the CLI and displayed.

### Scan via API Server (Cloud Mode)

1.  API Client sends `POST /scan` request with `{"targets": ["example.com"], "args": ["..."], "mode": "cloud"}`.
2.  API Server authenticates, validates, and generates a scan ID.
3.  Scan metadata is written to DynamoDB (status: `queued` or `starting`).
4.  API Server (acting as a client to the cloud orchestrator) invokes AWS Lambda for the targets.
5.  Lambda executes Nuclei, results might go to S3.
6.  API Server updates DynamoDB with progress/completion status (and S3 location if applicable).
7.  API Client polls `GET /scan/{scan_id}` to get status and results location.

## Infrastructure (Terraform)

-   The AWS infrastructure components (Lambda, S3, DynamoDB, ECS for API backend, IAM roles, etc.) are provisioned and managed using Terraform.
-   Refer to the [Terraform Setup Overview](../../terraform/README.md) and the [Deployment Guide](../../terraform/nuclear_pond_backend/DEPLOYMENT.md) for detailed information on the IaC setup.

This architecture allows Nuclear Pond to be adaptable, from simple local scans to complex, distributed scanning operations managed via an API. 