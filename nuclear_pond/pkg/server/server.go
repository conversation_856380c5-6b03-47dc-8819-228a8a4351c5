package server

import (
	"log"
	"net/http"
	"os"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

type Request struct {
	Targets []string `json:"Targets"`
	Batches int      `json:"Batches"`
	Threads int      `json:"Threads"`
	Args    string   `json:"Args"`
	Output  string   `json:"Output"`
}

type Response struct {
	RequestId string `json:"RequestId,omitempty"`
	Status    string `json:"status,omitempty"`
	Error     string `json:"error,omitempty"`
	Message   string `json:"message,omitempty"`
}

// CORS middleware using Chi
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		w.<PERSON>er().Set("Access-Control-Allow-Headers", "Content-Type, X-NuclearPond-API-Key")
		w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight OPTIONS request
		if r.Method == "OPTIONS" {
			log.Printf("CORS preflight request received for %s from origin: %s", r.URL.Path, r.Header.Get("Origin"))
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// API key authentication middleware
func apiKeyMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip API key check for health endpoint
		if r.URL.Path == "/health-check" {
			next.ServeHTTP(w, r)
			return
		}

		if !checkAPIKey(r) {
			log.Printf("Invalid API key for request %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, Response{
				Error:   "Unauthorized",
				Message: "Invalid or missing API key",
			})
			return
		}

		next.ServeHTTP(w, r)
	})
}

// Index handler
func indexHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Index request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	render.JSON(w, r, Response{
		Message: "Welcome to the Nuclear Pond API",
	})
}

// Health check handler
func healthHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Health check request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	render.JSON(w, r, Response{
		Status: "ok",
	})
}

// Scan status handler
func scanStatusHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan status request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)

	scanId := chi.URLParam(r, "scanId")
	if scanId == "" {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Missing scan ID parameter",
		})
		return
	}

	log.Printf("Getting scan state for: %s", scanId)

	state, err := getScanState(scanId)
	if err != nil {
		log.Printf("Error getting scan state for %s: %v", scanId, err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{
			Error:   "Internal Server Error",
			Message: "Error getting scan state: " + err.Error(),
		})
		return
	}

	render.JSON(w, r, Response{
		Status: state,
	})
}

// Scan handler
func scanHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Scan request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	log.Printf("Request headers: %v", r.Header)

	var req Request
	if err := render.DecodeJSON(r.Body, &req); err != nil {
		log.Printf("Error decoding JSON from %s: %v", r.RemoteAddr, err)
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Error decoding JSON: " + err.Error(),
		})
		return
	}

	log.Printf("Received scan request: %+v", req)

	// Validate request
	if len(req.Targets) == 0 {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{
			Error:   "Bad Request",
			Message: "Targets field is required and must contain at least one target",
		})
		return
	}

	scanId := uuid.New().String()
	go backgroundScan(req, scanId)

	log.Printf("Started background scan with ID: %s", scanId)

	render.Status(r, http.StatusAccepted)
	render.JSON(w, r, Response{
		RequestId: scanId,
		Message:   "Scan request accepted and queued for processing",
	})
}

// Server configuration check
func serverCheck() {
	// check if NUCLEARPOND_API_KEY is set
	if os.Getenv("NUCLEARPOND_API_KEY") == "" {
		log.Fatal("CRITICAL: NUCLEARPOND_API_KEY environment variable must be set.")
		os.Exit(1)
	}

	// check if AWS_REGION and AWS_LAMBDA_FUNCTION_NAME are set
	_, region := os.LookupEnv("AWS_REGION")
	if !region {
		log.Fatal("AWS_REGION not set")
		os.Exit(1)
	}
	_, function := os.LookupEnv("AWS_LAMBDA_FUNCTION_NAME")
	if !function {
		log.Fatal("AWS_LAMBDA_FUNCTION_NAME not set")
		os.Exit(1)
	}
	_, dynamodb := os.LookupEnv("AWS_DYNAMODB_TABLE")
	if !dynamodb {
		log.Fatal("AWS_DYNAMODB_TABLE not set")
		os.Exit(1)
	}
}

// Main server setup and routing
func HandleRequests() {
	// Load environment variables from .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found or error loading .env file:", err)
	}

	// Check if the server is configured correctly
	serverCheck()

	log.Printf("Starting Nuclear Pond server with API key configured: %t", os.Getenv("NUCLEARPOND_API_KEY") != "")

	// Create Chi router
	r := chi.NewRouter()

	// Global middleware
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(corsMiddleware)

	// Routes with API key protection (except health check)
	r.Route("/", func(r chi.Router) {
		r.Use(apiKeyMiddleware)
		r.Get("/", indexHandler)
		r.Post("/scan", scanHandler)
		r.Get("/scan/{scanId}", scanStatusHandler)
	})

	// Public health check endpoint (no API key required)
	r.Get("/health-check", healthHandler)

	log.Println("Nuclear Pond server starting on port 8080...")
	log.Println("CORS enabled for all origins")
	log.Println("API key authentication enabled for protected endpoints")

	if err := http.ListenAndServe(":8080", r); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}
